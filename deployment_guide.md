# Deployment Configuration for WhatsApp AI Agent Web Portal

This document outlines the deployment configuration for the WhatsApp AI Agent Web Portal, a Flask application that serves as the user interface for managing the WhatsApp AI Agent service.

## Prerequisites

Before deploying the application, ensure you have the following:

1. A server with Python 3.8+ installed
2. MySQL database server
3. WhatsApp Business API credentials
4. Stripe API credentials for payment processing

## Environment Variables

The application requires the following environment variables to be set:

```
# Database Configuration
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_HOST=your_db_host
DB_PORT=3306
DB_NAME=whatsapp_ai_portal

# WhatsApp API Configuration
WHATSAPP_API_KEY=your_whatsapp_api_key
WHATSAPP_API_URL=https://api.whatsapp.com/v1
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Flask Configuration
FLASK_SECRET_KEY=your_flask_secret_key
FLASK_ENV=production
```

## Deployment Steps

1. Clone the repository to your server
2. Create a virtual environment and install dependencies
3. Set up the MySQL database
4. Configure environment variables
5. Set up a production WSGI server (Gunicorn recommended)
6. Configure Nginx as a reverse proxy
7. Set up SSL certificates
8. Start the application

## Detailed Deployment Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/whatsapp-ai-portal.git
cd whatsapp-ai-portal
```

### 2. Create Virtual Environment and Install Dependencies

```bash
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 3. Set Up MySQL Database

```bash
mysql -u root -p
```

```sql
CREATE DATABASE whatsapp_ai_portal;
CREATE USER 'whatsapp_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON whatsapp_ai_portal.* TO 'whatsapp_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. Configure Environment Variables

Create a `.env` file in the project root directory with the environment variables listed above.

### 5. Initialize the Database

```bash
flask db init
flask db migrate
flask db upgrade
```

### 6. Set Up Gunicorn

Install Gunicorn:

```bash
pip install gunicorn
```

Create a systemd service file at `/etc/systemd/system/whatsapp-ai-portal.service`:

```
[Unit]
Description=WhatsApp AI Portal
After=network.target

[Service]
User=ubuntu
Group=www-data
WorkingDirectory=/path/to/whatsapp-ai-portal
Environment="PATH=/path/to/whatsapp-ai-portal/venv/bin"
EnvironmentFile=/path/to/whatsapp-ai-portal/.env
ExecStart=/path/to/whatsapp-ai-portal/venv/bin/gunicorn --workers 4 --bind 0.0.0.0:8000 "src.main:app"
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl enable whatsapp-ai-portal
sudo systemctl start whatsapp-ai-portal
```

### 7. Configure Nginx as Reverse Proxy

Install Nginx:

```bash
sudo apt update
sudo apt install nginx
```

Create a Nginx configuration file at `/etc/nginx/sites-available/whatsapp-ai-portal`:

```
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/whatsapp-ai-portal /etc/nginx/sites-enabled
sudo nginx -t
sudo systemctl restart nginx
```

### 8. Set Up SSL with Certbot

Install Certbot:

```bash
sudo apt install certbot python3-certbot-nginx
```

Obtain and configure SSL certificate:

```bash
sudo certbot --nginx -d your-domain.com
```

### 9. Verify Deployment

Visit your domain in a web browser to verify that the application is running correctly.

## Monitoring and Maintenance

### Logs

Application logs can be viewed with:

```bash
sudo journalctl -u whatsapp-ai-portal
```

### Updates

To update the application:

1. Pull the latest code from the repository
2. Install any new dependencies
3. Run database migrations if needed
4. Restart the service

```bash
cd /path/to/whatsapp-ai-portal
git pull
source venv/bin/activate
pip install -r requirements.txt
flask db upgrade
sudo systemctl restart whatsapp-ai-portal
```

## Backup Strategy

### Database Backup

Set up a cron job to regularly backup the MySQL database:

```bash
crontab -e
```

Add the following line to backup the database daily at 2 AM:

```
0 2 * * * mysqldump -u whatsapp_user -p'your_password' whatsapp_ai_portal > /path/to/backups/whatsapp_ai_portal_$(date +\%Y\%m\%d).sql
```

### Application Backup

Regularly backup the application code and configuration files:

```bash
crontab -e
```

Add the following line to backup the application weekly:

```
0 3 * * 0 tar -czf /path/to/backups/whatsapp_ai_portal_$(date +\%Y\%m\%d).tar.gz /path/to/whatsapp-ai-portal
```

## Troubleshooting

### Common Issues

1. **Application not starting**: Check the logs with `sudo journalctl -u whatsapp-ai-portal`
2. **Database connection issues**: Verify database credentials and connectivity
3. **Nginx configuration errors**: Check Nginx logs with `sudo tail -f /var/log/nginx/error.log`
4. **SSL certificate issues**: Run `sudo certbot certificates` to check certificate status

### Support Contacts

For additional support, contact:
- Technical Support: <EMAIL>
- Emergency Contact: +1 (555) 123-4567
