// Main JavaScript for WhatsApp AI Agent Web Portal

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
    });

    // Flash message auto-dismiss
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert.alert-success, .alert.alert-info');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Credit package selection
    var creditPackages = document.querySelectorAll('.credit-package');
    if (creditPackages.length > 0) {
        creditPackages.forEach(function(package) {
            package.addEventListener('click', function() {
                // Remove selected class from all packages
                creditPackages.forEach(function(p) {
                    p.classList.remove('selected');
                });
                
                // Add selected class to clicked package
                this.classList.add('selected');
                
                // Check the radio button
                var radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });
    }

    // Payment method selection
    var paymentMethods = document.querySelectorAll('.payment-method-card');
    if (paymentMethods.length > 0) {
        paymentMethods.forEach(function(method) {
            method.addEventListener('click', function() {
                var radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });
    }

    // Form validation
    var forms = document.querySelectorAll('.needs-validation');
    if (forms.length > 0) {
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }

    // Password strength meter
    var passwordInput = document.getElementById('password');
    var passwordStrength = document.getElementById('password-strength');
    
    if (passwordInput && passwordStrength) {
        passwordInput.addEventListener('input', function() {
            var password = passwordInput.value;
            var strength = 0;
            
            if (password.length >= 8) strength += 1;
            if (password.match(/[a-z]+/)) strength += 1;
            if (password.match(/[A-Z]+/)) strength += 1;
            if (password.match(/[0-9]+/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;
            
            switch (strength) {
                case 0:
                case 1:
                    passwordStrength.className = 'progress-bar bg-danger';
                    passwordStrength.style.width = '20%';
                    passwordStrength.textContent = 'Very Weak';
                    break;
                case 2:
                    passwordStrength.className = 'progress-bar bg-warning';
                    passwordStrength.style.width = '40%';
                    passwordStrength.textContent = 'Weak';
                    break;
                case 3:
                    passwordStrength.className = 'progress-bar bg-info';
                    passwordStrength.style.width = '60%';
                    passwordStrength.textContent = 'Medium';
                    break;
                case 4:
                    passwordStrength.className = 'progress-bar bg-primary';
                    passwordStrength.style.width = '80%';
                    passwordStrength.textContent = 'Strong';
                    break;
                case 5:
                    passwordStrength.className = 'progress-bar bg-success';
                    passwordStrength.style.width = '100%';
                    passwordStrength.textContent = 'Very Strong';
                    break;
            }
        });
    }

    // Date range picker initialization
    var dateRangeInputs = document.querySelectorAll('.date-range-picker');
    if (dateRangeInputs.length > 0) {
        dateRangeInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                var form = this.closest('form');
                if (form) {
                    form.submit();
                }
            });
        });
    }

    // WhatsApp verification status checker
    var verificationStatusChecker = document.getElementById('verification-status-checker');
    if (verificationStatusChecker) {
        function checkStatus() {
            fetch(verificationStatusChecker.dataset.url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.is_verified) {
                        window.location.reload();
                    } else {
                        setTimeout(checkStatus, 5000); // Check every 5 seconds
                    }
                })
                .catch(error => {
                    console.error('Error checking verification status:', error);
                    setTimeout(checkStatus, 10000); // Retry after 10 seconds on error
                });
        }
        
        checkStatus();
    }

    // Copy to clipboard functionality
    var copyButtons = document.querySelectorAll('.copy-to-clipboard');
    if (copyButtons.length > 0) {
        copyButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var textToCopy = this.dataset.clipboard;
                var tempInput = document.createElement('input');
                tempInput.value = textToCopy;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                
                // Show copied tooltip
                var originalTitle = this.getAttribute('title');
                this.setAttribute('title', 'Copied!');
                var tooltip = bootstrap.Tooltip.getInstance(this);
                if (tooltip) {
                    tooltip.show();
                }
                
                // Reset tooltip after 2 seconds
                setTimeout(() => {
                    this.setAttribute('title', originalTitle);
                    if (tooltip) {
                        tooltip.hide();
                    }
                }, 2000);
            });
        });
    }

    // Animated counters
    var counters = document.querySelectorAll('.counter-value');
    if (counters.length > 0) {
        counters.forEach(function(counter) {
            var target = parseInt(counter.getAttribute('data-target'));
            var count = 0;
            var speed = 2000 / target;
            
            var interval = setInterval(function() {
                count += 1;
                counter.textContent = count;
                
                if (count >= target) {
                    clearInterval(interval);
                    counter.textContent = target;
                }
            }, speed);
        });
    }

    // Mobile sidebar toggle
    var sidebarToggle = document.getElementById('sidebar-toggle');
    var sidebar = document.getElementById('sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }
});
