"""
User model for the WhatsApp AI Agent Web Portal
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

from src.main import db

class User(db.Model):
    """User model for authentication and profile management"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    first_name = db.Column(db.String(50), nullable=True)
    last_name = db.Column(db.String(50), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    
    # Relationships
    payment_methods = db.relationship('PaymentMethod', backref='user', lazy=True)
    transactions = db.relationship('Transaction', backref='user', lazy=True)
    credits = db.relationship('Credit', backref='user', uselist=False, lazy=True)
    whatsapp_verification = db.relationship('WhatsappVerification', backref='user', uselist=False, lazy=True)
    usage_logs = db.relationship('UsageLog', backref='user', lazy=True)
    
    def __init__(self, username, email, password_hash, first_name=None, last_name=None, is_admin=False):
        self.id = str(uuid.uuid4())
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.first_name = first_name
        self.last_name = last_name
        self.is_admin = is_admin
    
    def __repr__(self):
        return f'<User {self.username}>'
