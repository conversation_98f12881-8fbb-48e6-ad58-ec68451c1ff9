<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp AI Agent - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('admin.dashboard') }}">
                <i class="fab fa-whatsapp me-2"></i>WhatsApp AI Agent Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item d-lg-none">
                        <a class="nav-link active" href="{{ url_for('admin.dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('admin.users') }}">Users</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('admin.payments') }}">Payments</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('admin.usage') }}">Usage</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('admin.settings') }}">Settings</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">Admin: {{ session.get('username') }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('admin.dashboard') }}" class="nav-link active">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('admin.users') }}" class="nav-link">
                                <i class="fas fa-users"></i> Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('admin.payments') }}" class="nav-link">
                                <i class="fas fa-money-bill-wave"></i> Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('admin.usage') }}" class="nav-link">
                                <i class="fas fa-chart-line"></i> Usage
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('admin.settings') }}" class="nav-link">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 py-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h3 mb-4">Admin Dashboard</h1>

                <!-- Overview Stats -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="admin-stat-card">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="stat-label">Total Users</div>
                                <i class="fas fa-users fa-2x text-primary"></i>
                            </div>
                            <div class="stat-value counter-value" data-target="{{ stats.total_users }}">0</div>
                            <div class="small text-muted">
                                <span class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>{{ stats.new_users_today }}
                                </span>
                                new today
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="admin-stat-card">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="stat-label">Active WhatsApp</div>
                                <i class="fab fa-whatsapp fa-2x text-primary"></i>
                            </div>
                            <div class="stat-value counter-value" data-target="{{ stats.active_whatsapp }}">0</div>
                            <div class="small text-muted">
                                <span class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>{{ stats.new_whatsapp_today }}
                                </span>
                                new today
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="admin-stat-card">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="stat-label">Revenue (USD)</div>
                                <i class="fas fa-dollar-sign fa-2x text-primary"></i>
                            </div>
                            <div class="stat-value">${{ stats.total_revenue }}</div>
                            <div class="small text-muted">
                                <span class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>${{ stats.revenue_today }}
                                </span>
                                today
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="admin-stat-card">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="stat-label">Total Credits Sold</div>
                                <i class="fas fa-coins fa-2x text-primary"></i>
                            </div>
                            <div class="stat-value counter-value" data-target="{{ stats.total_credits_sold }}">0</div>
                            <div class="small text-muted">
                                <span class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i>{{ stats.credits_sold_today }}
                                </span>
                                today
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage Stats -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h2 class="h5 mb-0">Usage Trends</h2>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary active" data-period="week">Week</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" data-period="month">Month</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" data-period="year">Year</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="usageChart" height="250"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">Service Distribution</h2>
                            </div>
                            <div class="card-body">
                                <canvas id="serviceDistributionChart" height="250"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h2 class="h5 mb-0">Recent Users</h2>
                                    <a href="{{ url_for('admin.users') }}" class="btn btn-sm btn-outline-primary">View All</a>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Registered</th>
                                                <th>Status</th>
                                                <th>Credits</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for user in recent_users %}
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="testimonial-avatar me-2">
                                                                <span>{{ user.username[0]|upper }}</span>
                                                            </div>
                                                            <div>
                                                                <div>{{ user.username }}</div>
                                                                <small class="text-muted">{{ user.email }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                                    <td>
                                                        {% if user.is_active %}
                                                            <span class="badge bg-success">Active</span>
                                                        {% else %}
                                                            <span class="badge bg-danger">Inactive</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ user.credit_balance }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h2 class="h5 mb-0">Recent Payments</h2>
                                    <a href="{{ url_for('admin.payments') }}" class="btn btn-sm btn-outline-primary">View All</a>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Amount</th>
                                                <th>Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for payment in recent_payments %}
                                                <tr>
                                                    <td>{{ payment.username }}</td>
                                                    <td>${{ payment.amount }}</td>
                                                    <td>{{ payment.created_at.strftime('%Y-%m-%d') }}</td>
                                                    <td>
                                                        {% if payment.status == 'completed' %}
                                                            <span class="badge bg-success">Completed</span>
                                                        {% elif payment.status == 'pending' %}
                                                            <span class="badge bg-warning">Pending</span>
                                                        {% elif payment.status == 'failed' %}
                                                            <span class="badge bg-danger">Failed</spa
(Content truncated due to size limit. Use line ranges to read in chunks)