<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp AI Agent - Help & Support</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fab fa-whatsapp me-2"></i>WhatsApp AI Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.buy_credits') }}">Buy Credits</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('whatsapp.whatsapp_connection') }}">WhatsApp Connection</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.usage_history') }}">Usage History</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.payment_methods') }}">Payment Methods</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.profile') }}">Profile</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link active" href="{{ url_for('dashboard.help') }}">Help & Support</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">Welcome, {{ session.get('username') }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.index') }}" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.buy_credits') }}" class="nav-link">
                                <i class="fas fa-coins"></i> Buy Credits
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('whatsapp.whatsapp_connection') }}" class="nav-link">
                                <i class="fab fa-whatsapp"></i> WhatsApp Connection
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.usage_history') }}" class="nav-link">
                                <i class="fas fa-history"></i> Usage History
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.payment_methods') }}" class="nav-link">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.profile') }}" class="nav-link">
                                <i class="fas fa-user-circle"></i> Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.help') }}" class="nav-link active">
                                <i class="fas fa-question-circle"></i> Help & Support
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 py-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h3 mb-4">Help & Support</h1>

                <div class="row">
                    <div class="col-lg-8">
                        <!-- FAQ Section -->
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">Frequently Asked Questions</h2>
                            </div>
                            <div class="card-body">
                                <div class="accordion" id="faqAccordion">
                                    <div class="accordion-item border-0 mb-3 shadow-sm">
                                        <h2 class="accordion-header" id="headingOne">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                                How do I connect my WhatsApp account?
                                            </button>
                                        </h2>
                                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                <p>To connect your WhatsApp account:</p>
                                                <ol>
                                                    <li>Go to the WhatsApp Connection page in your dashboard</li>
                                                    <li>Enter your phone number with country code (e.g., +**********)</li>
                                                    <li>Click "Connect WhatsApp"</li>
                                                    <li>You'll receive a verification code</li>
                                                    <li>Send this code to our WhatsApp business number</li>
                                                    <li>Once verified, you can start using the service</li>
                                                </ol>
                                                <p>If you encounter any issues, please contact our support team.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item border-0 mb-3 shadow-sm">
                                        <h2 class="accordion-header" id="headingTwo">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                How does the credit system work?
                                            </button>
                                        </h2>
                                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                <p>Our pay-as-you-go model uses credits for different services:</p>
                                                <ul>
                                                    <li><strong>Text Questions:</strong> 1 credit per question</li>
                                                    <li><strong>Image Questions:</strong> 2 credits per image</li>
                                                    <li><strong>Document Processing:</strong> 5 credits per document</li>
                                                    <li><strong>Question Paper Processing:</strong> 10 credits per paper</li>
                                                    <li><strong>PDF Generation:</strong> 2 credits per PDF</li>
                                                </ul>
                                                <p>You can purchase credit packages starting at $9.99 for 50 credits. Credits never expire, and you can track your usage in your dashboard.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item border-0 mb-3 shadow-sm">
                                        <h2 class="accordion-header" id="headingThree">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                                What types of questions can I ask?
                                            </button>
                                        </h2>
                                        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                <p>You can ask virtually any type of question, including:</p>
                                                <ul>
                                                    <li>Academic questions (math, science, history, literature, etc.)</li>
                                                    <li>General knowledge questions</li>
                                                    <li>Research-related questions</li>
                                                    <li>Problem-solving questions</li>
                                                    <li>Analytical questions</li>
                                                </ul>
                                                <p>Our AI is trained on a vast knowledge base and can provide accurate answers to a wide range of topics. For specialized or technical questions, the AI will provide the best available information along with relevant sources when applicable.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item border-0 mb-3 shadow-sm">
                                        <h2 class="accordion-header" id="headingFour">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                                How do I upload a document or question paper?
                                            </button>
                                        </h2>
                                        <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                <p>To upload a document or question paper:</p>
                                                <ol>
                                                    <li>Open your WhatsApp chat with our service</li>
                                                    <li>Tap the attachment icon (paperclip or + symbol)</li>
                                                    <li>Select "Document" or "Gallery" depending on where your file is stored</li>
                                                    <li>Choose the document or image of the question paper</li>
                                                    <li>Add any specific instructions in the caption (optional)</li>
                                                    <li>Send the document</li>
                                                </ol>
                                                <p>Our AI will process the document, extract all questions, and provide answers. For question papers, it will answer each question individually and can provide a comprehensive PDF with all answers if requested.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item border-0 shadow-sm">
                                        <h2 class="accordion-header" id="headingFive">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                                                How do I get answers in PDF format?
                                            </button>
                                        </h2>
                                        <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#faqAccordion">
                                            <div class="accordion-body">
                                                <p>To receive answers in PDF format, simply add "PDF please" or "Send as PDF" to your message when asking a question or uploading a document. For example:</p>
                                                <ul>
                                                    <li>"What are the causes of climate change? PDF please"</li>
                                                    <li>"[Document upload] Please provide answers in PDF format"</li>
                                                </ul>
                                                <p>The AI will generate a well-formatted PDF document containing the question(s) and comprehensive answer(s). This feature costs 2 additional credits but provides a professional document that's easy to save, print, or share.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
      
(Content truncated due to size limit. Use line ranges to read in chunks)