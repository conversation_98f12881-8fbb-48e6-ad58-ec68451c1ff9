<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp AI Agent - Usage History</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fab fa-whatsapp me-2"></i>WhatsApp AI Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.buy_credits') }}">Buy Credits</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('whatsapp.whatsapp_connection') }}">WhatsApp Connection</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link active" href="{{ url_for('dashboard.usage_history') }}">Usage History</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.payment_methods') }}">Payment Methods</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.profile') }}">Profile</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">Welcome, {{ session.get('username') }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.index') }}" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.buy_credits') }}" class="nav-link">
                                <i class="fas fa-coins"></i> Buy Credits
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('whatsapp.whatsapp_connection') }}" class="nav-link">
                                <i class="fab fa-whatsapp"></i> WhatsApp Connection
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.usage_history') }}" class="nav-link active">
                                <i class="fas fa-history"></i> Usage History
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.payment_methods') }}" class="nav-link">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.profile') }}" class="nav-link">
                                <i class="fas fa-user-circle"></i> Profile
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 py-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h3 mb-4">Usage History</h1>

                <!-- Filter Controls -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <form action="{{ url_for('dashboard.usage_history') }}" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="service_type" class="form-label">Service Type</label>
                                <select class="form-select" id="service_type" name="service_type">
                                    <option value="">All Services</option>
                                    <option value="text_question" {% if request.args.get('service_type') == 'text_question' %}selected{% endif %}>Text Questions</option>
                                    <option value="image_question" {% if request.args.get('service_type') == 'image_question' %}selected{% endif %}>Image Questions</option>
                                    <option value="document_processing" {% if request.args.get('service_type') == 'document_processing' %}selected{% endif %}>Document Processing</option>
                                    <option value="pdf_generation" {% if request.args.get('service_type') == 'pdf_generation' %}selected{% endif %}>PDF Generation</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="date_range" class="form-label">Date Range</label>
                                <select class="form-select date-range-picker" id="date_range" name="date_range">
                                    <option value="all" {% if request.args.get('date_range') == 'all' or not request.args.get('date_range') %}selected{% endif %}>All Time</option>
                                    <option value="today" {% if request.args.get('date_range') == 'today' %}selected{% endif %}>Today</option>
                                    <option value="yesterday" {% if request.args.get('date_range') == 'yesterday' %}selected{% endif %}>Yesterday</option>
                                    <option value="this_week" {% if request.args.get('date_range') == 'this_week' %}selected{% endif %}>This Week</option>
                                    <option value="last_week" {% if request.args.get('date_range') == 'last_week' %}selected{% endif %}>Last Week</option>
                                    <option value="this_month" {% if request.args.get('date_range') == 'this_month' %}selected{% endif %}>This Month</option>
                                    <option value="last_month" {% if request.args.get('date_range') == 'last_month' %}selected{% endif %}>Last Month</option>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-2"></i> Apply Filters
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm stat-card">
                            <div class="card-body">
                                <h3 class="h6 text-muted">Total Credits Used</h3>
                                <p class="h3 mb-0">{{ usage_stats.total_credits_used }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm stat-card">
                            <div class="card-body">
                                <h3 class="h6 text-muted">Text Questions</h3>
                                <p class="h3 mb-0">{{ usage_stats.by_type.get('text_question', {}).get('count', 0) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm stat-card">
                            <div class="card-body">
                                <h3 class="h6 text-muted">Image Questions</h3>
                                <p class="h3 mb-0">{{ usage_stats.by_type.get('image_question', {}).get('count', 0) }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm stat-card">
                            <div class="card-body">
                                <h3 class="h6 text-muted">Documents Processed</h3>
                                <p class="h3 mb-0">{{ usage_stats.by_type.get('document_processing', {}).get('count', 0) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage History Table -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        {% if usage_logs %}
                            <div class="table-responsive">
                                <table class="table table-hover usage-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Service</th>
                                            <th>Credits Used</th>
                                            <th>Details</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for log in usage_logs %}
                                            <tr>
                                                <td>{{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                <td>
                                                    <span class="service-badge {{ log.service_type }}">
                                                        {{ log.service_type|replace('_', ' ')|title }}
                                                    </span>
                                                </td>
                                                <td>{{ log.credits_used }}</td>
                                                <td>
                                                    {% if log.details %}
                                                        <button type="button" class="btn btn-sm btn-link" data-bs-toggle="modal" data-bs-target="#detailsModal{{ log.id }}">
                                                            View Details
                                                        </button>
                                                        
                                                        <!-- Details Modal -->
                                                        <div class="modal fade" id="detailsModal{{ log.id }}" tabindex="-1" aria-labelledby="detailsModalLabel{{ log.id }}" aria-hidden="true">
                                                            <div class="modal-dialog">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title" id="detailsModalLabel{{ log.id }}">Usage Details</h5>
                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                        <p><strong>Date:</strong> {{ log.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                                                                        <p><strong>Service:</strong> {{ log.service_type|replace('_', ' ')|title }}</p>
                                                                        <p><strong>Credits Used:</strong> {{ log.credits_used }}</p>
                                                                        <p><strong>Details:</strong></p>
                                                                        <pre class="bg-light p-3 rounded">{{ log.details }}</pre>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% else %}
                                                        <span class="text-muted">No details available</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            {% if pagination.pages > 1 %}
                                <div class="d-flex justify-content-center py-3">
                                    <nav aria-label="Usage history pagination">
                                        <ul class="pagination">
                                            <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                                                <a class="page-link" href="{{ url_for('dashboard.usage_history', page=pagination.page-1, service_type=request.args.get('servic
(Content truncated due to size limit. Use line ranges to read in chunks)