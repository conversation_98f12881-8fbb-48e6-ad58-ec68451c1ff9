"""
API routes for the WhatsApp AI Agent Web Portal
"""

from flask import Blueprint, jsonify, request, session
from src.models.user import User
from src.models.credit import Credit
from src.models.whatsapp import WhatsappVerification
from src.models.usage import UsageLog
from src.main import db
from functools import wraps
import uuid
from datetime import datetime

api_bp = Blueprint('api', __name__)

# API authentication decorator
def api_auth_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': 'Authentication required'}), 401
        
        token = auth_header.split(' ')[1]
        
        # In a real implementation, validate JWT token
        # For now, just check if it's a valid user ID
        user = User.query.get(token)
        
        if not user:
            return jsonify({'success': False, 'message': 'Invalid token'}), 401
        
        if not user.is_active:
            return jsonify({'success': False, 'message': 'User account is inactive'}), 403
        
        # Set user for the request
        request.current_user = user
        
        return f(*args, **kwargs)
    return decorated_function

@api_bp.route('/api/user/info')
@api_auth_required
def user_info():
    """Get user information"""
    user = request.current_user
    
    # Get credit balance
    credit = Credit.query.filter_by(user_id=user.id).first()
    credit_balance = credit.balance if credit else 0.0
    
    # Get WhatsApp verification status
    whatsapp = WhatsappVerification.query.filter_by(user_id=user.id).first()
    whatsapp_status = {
        'connected': whatsapp.is_verified if whatsapp else False,
        'phone_number': whatsapp.phone_number if whatsapp else None
    }
    
    return jsonify({
        'success': True,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'created_at': user.created_at.isoformat(),
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'is_active': user.is_active,
            'is_admin': user.is_admin
        },
        'credit_balance': credit_balance,
        'whatsapp_status': whatsapp_status
    })

@api_bp.route('/api/usage/log', methods=['POST'])
@api_auth_required
def log_usage():
    """Log service usage"""
    user = request.current_user
    
    data = request.json
    
    if not data:
        return jsonify({'success': False, 'message': 'Invalid request'}), 400
    
    service_type = data.get('service_type')
    credits_used = data.get('credits_used')
    details = data.get('details')
    
    if not service_type or credits_used is None:
        return jsonify({'success': False, 'message': 'Service type and credits used are required'}), 400
    
    # Check if user has enough credits
    credit = Credit.query.filter_by(user_id=user.id).first()
    
    if not credit or credit.balance < credits_used:
        return jsonify({'success': False, 'message': 'Insufficient credits'}), 403
    
    try:
        # Create usage log
        usage_log = UsageLog(
            user_id=user.id,
            service_type=service_type,
            credits_used=credits_used,
            details=details
        )
        db.session.add(usage_log)
        
        # Update credit balance
        credit.balance -= credits_used
        credit.last_updated = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Usage logged successfully',
            'usage_log_id': usage_log.id,
            'new_balance': credit.balance
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/api/whatsapp/verify', methods=['POST'])
def verify_whatsapp():
    """Verify WhatsApp number (called by WhatsApp webhook)"""
    data = request.json
    
    if not data:
        return jsonify({'success': False, 'message': 'Invalid request'}), 400
    
    phone_number = data.get('phone_number')
    verification_code = data.get('verification_code')
    
    if not phone_number or not verification_code:
        return jsonify({'success': False, 'message': 'Phone number and verification code are required'}), 400
    
    # Find verification record
    verification = WhatsappVerification.query.filter_by(
        phone_number=phone_number,
        verification_code=verification_code,
        is_verified=False
    ).first()
    
    if not verification:
        return jsonify({'success': False, 'message': 'Invalid verification code'}), 400
    
    try:
        # Update verification status
        verification.is_verified = True
        verification.verified_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'WhatsApp number verified successfully',
            'user_id': verification.user_id
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500
