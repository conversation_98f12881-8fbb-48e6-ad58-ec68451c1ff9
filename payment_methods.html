<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp AI Agent - Payment Methods</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fab fa-whatsapp me-2"></i>WhatsApp AI Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.buy_credits') }}">Buy Credits</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('whatsapp.whatsapp_connection') }}">WhatsApp Connection</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.usage_history') }}">Usage History</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link active" href="{{ url_for('payment.payment_methods') }}">Payment Methods</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.profile') }}">Profile</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">Welcome, {{ session.get('username') }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.index') }}" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.buy_credits') }}" class="nav-link">
                                <i class="fas fa-coins"></i> Buy Credits
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('whatsapp.whatsapp_connection') }}" class="nav-link">
                                <i class="fab fa-whatsapp"></i> WhatsApp Connection
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.usage_history') }}" class="nav-link">
                                <i class="fas fa-history"></i> Usage History
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.payment_methods') }}" class="nav-link active">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.profile') }}" class="nav-link">
                                <i class="fas fa-user-circle"></i> Profile
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 py-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0">Payment Methods</h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="{{ url_for('payment.add_payment_method') }}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i> Add Payment Method
                        </a>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-4">
                        {% if payment_methods %}
                            {% for method in payment_methods %}
                                <div class="payment-method-card {% if method.is_default %}default{% endif %} mb-3">
                                    <div class="row align-items-center">
                                        <div class="col-md-1 text-center">
                                            {% if method.payment_type == 'credit_card' %}
                                                <i class="far fa-credit-card fa-2x text-primary"></i>
                                            {% elif method.payment_type == 'paypal' %}
                                                <i class="fab fa-paypal fa-2x text-primary"></i>
                                            {% else %}
                                                <i class="fas fa-money-bill-wave fa-2x text-primary"></i>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-7">
                                            <h3 class="h5 mb-1">
                                                {% if method.payment_type == 'credit_card' %}
                                                    Credit Card ending in {{ method.card_last_four }}
                                                {% elif method.payment_type == 'paypal' %}
                                                    PayPal Account ({{ method.email }})
                                                {% else %}
                                                    {{ method.payment_type|title }}
                                                {% endif %}
                                            </h3>
                                            <p class="small text-muted mb-0">
                                                {% if method.payment_type == 'credit_card' %}
                                                    Expires: {{ method.expiry_date }}
                                                {% elif method.payment_type == 'paypal' %}
                                                    Connected on {{ method.created_at.strftime('%Y-%m-%d') }}
                                                {% else %}
                                                    Added on {{ method.created_at.strftime('%Y-%m-%d') }}
                                                {% endif %}
                                                {% if method.is_default %}
                                                    <span class="badge bg-primary ms-2">Default</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-md-end mt-3 mt-md-0">
                                            {% if not method.is_default %}
                                                <form action="{{ url_for('payment.set_default_payment_method', method_id=method.id) }}" method="post" class="d-inline">
                                                    <button type="submit" class="btn btn-sm btn-outline-primary me-2">
                                                        Set as Default
                                                    </button>
                                                </form>
                                            {% endif %}
                                            <form action="{{ url_for('payment.delete_payment_method', method_id=method.id) }}" method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this payment method?');">
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-credit-card fa-4x text-muted mb-3"></i>
                                <h3 class="h5 mb-3">No Payment Methods</h3>
                                <p class="mb-4">You haven't added any payment methods yet.</p>
                                <a href="{{ url_for('payment.add_payment_method') }}" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-2"></i> Add Payment Method
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Payment History -->
                <h2 class="h4 mb-3">Payment History</h2>
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        {% if payment_history %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Payment Method</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in payment_history %}
                                            <tr>
                                                <td>{{ payment.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                <td>{{ payment.description }}</td>
                                                <td>${{ payment.amount }}</td>
                                                <td>
                                                    {% if payment.status == 'completed' %}
                                                        <span class="badge bg-success">Completed</span>
                                                    {% elif payment.status == 'pending' %}
                                                        <span class="badge bg-warning">Pending</span>
                                                    {% elif payment.status == 'failed' %}
                                                        <span class="badge bg-danger">Failed</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ payment.status|title }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if payment.payment_method_type == 'credit_card' %}
                                                        <i class="far fa-credit-card me-1"></i> Card ending in {{ payment.payment_method_details }}
                                                    {% elif payment.payment_method_type == 'paypal' %}
                                                        <i class="fab fa-paypal me-1"></i> PayPal
                                                    {% else %}
                                                        <i class="fas fa-money-bill-wave me-1"></i> {{ payment.payment_method_type|title }}
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                <p class="mb-0">No payment history to display</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
