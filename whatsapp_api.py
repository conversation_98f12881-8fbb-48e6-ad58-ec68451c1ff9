"""
WhatsApp Business API integration for the WhatsApp AI Agent
"""

import requests
import json
import os
import uuid
import logging
from datetime import datetime, timedelta
from flask import current_app

logger = logging.getLogger(__name__)

class WhatsAppBusinessAPI:
    """
    Class to handle interactions with the WhatsApp Business API
    """
    
    def __init__(self, api_key=None):
        """Initialize the WhatsApp Business API client"""
        self.api_key = api_key or os.environ.get('WHATSAPP_API_KEY')
        self.api_url = os.environ.get('WHATSAPP_API_URL', 'https://api.whatsapp.com/v1')
        self.phone_number_id = os.environ.get('WHATSAPP_PHONE_NUMBER_ID')
        
        if not self.api_key:
            logger.warning("WhatsApp API key not provided")
        
        if not self.phone_number_id:
            logger.warning("WhatsApp Phone Number ID not provided")
    
    def send_message(self, to_phone_number, message_text):
        """
        Send a text message to a WhatsApp user
        
        Args:
            to_phone_number (str): Recipient's phone number with country code
            message_text (str): Message content to send
            
        Returns:
            dict: API response
        """
        if not self.api_key or not self.phone_number_id:
            logger.error("WhatsApp API credentials not configured")
            return {"error": "API credentials not configured"}
        
        # Ensure phone number is in correct format
        to_phone_number = self._format_phone_number(to_phone_number)
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to_phone_number,
            "type": "text",
            "text": {
                "body": message_text
            }
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/messages",
                headers=headers,
                data=json.dumps(payload)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to send WhatsApp message: {response.text}")
                return {"error": f"API request failed with status {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.exception(f"Exception sending WhatsApp message: {str(e)}")
            return {"error": str(e)}
    
    def send_template_message(self, to_phone_number, template_name, template_params=None):
        """
        Send a template message to a WhatsApp user
        
        Args:
            to_phone_number (str): Recipient's phone number with country code
            template_name (str): Name of the template to use
            template_params (list): List of parameters for the template
            
        Returns:
            dict: API response
        """
        if not self.api_key or not self.phone_number_id:
            logger.error("WhatsApp API credentials not configured")
            return {"error": "API credentials not configured"}
        
        # Ensure phone number is in correct format
        to_phone_number = self._format_phone_number(to_phone_number)
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        components = []
        if template_params:
            components.append({
                "type": "body",
                "parameters": [{"type": "text", "text": param} for param in template_params]
            })
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to_phone_number,
            "type": "template",
            "template": {
                "name": template_name,
                "language": {
                    "code": "en_US"
                },
                "components": components if components else []
            }
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/messages",
                headers=headers,
                data=json.dumps(payload)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to send WhatsApp template message: {response.text}")
                return {"error": f"API request failed with status {response.status_code}: {response.text}"}
                
        except Exception as e:
            logger.exception(f"Exception sending WhatsApp template message: {str(e)}")
            return {"error": str(e)}
    
    def send_verification_code(self, to_phone_number, verification_code):
        """
        Send a verification code to a WhatsApp user
        
        Args:
            to_phone_number (str): Recipient's phone number with country code
            verification_code (str): Verification code to send
            
        Returns:
            dict: API response
        """
        message = (
            f"Your WhatsApp AI Agent verification code is: {verification_code}\n\n"
            f"This code will expire in 10 minutes. Please do not share this code with anyone."
        )
        
        return self.send_message(to_phone_number, message)
    
    def send_welcome_message(self, to_phone_number, user_name):
        """
        Send a welcome message to a newly verified WhatsApp user
        
        Args:
            to_phone_number (str): Recipient's phone number with country code
            user_name (str): User's name for personalization
            
        Returns:
            dict: API response
        """
        message = (
            f"Welcome to WhatsApp AI Agent, {user_name}! 🎉\n\n"
            f"Your WhatsApp number has been successfully verified. You can now start using our AI agent to answer your questions.\n\n"
            f"Here's how to use the service:\n"
            f"• Send a text question directly in a message\n"
            f"• Send an image containing text or questions\n"
            f"• Upload documents or question papers\n"
            f"• Type \"PDF please\" to get answers in PDF format\n\n"
            f"Need help? Just type \"help\" or visit your dashboard at any time."
        )
        
        return self.send_message(to_phone_number, message)
    
    def process_incoming_message(self, message_data):
        """
        Process an incoming message from the WhatsApp API webhook
        
        Args:
            message_data (dict): Message data from webhook
            
        Returns:
            dict: Processing result
        """
        try:
            # Extract message details
            if 'messages' not in message_data or not message_data['messages']:
                return {"error": "No messages in webhook data"}
            
            message = message_data['messages'][0]
            from_number = message.get('from')
            message_id = message.get('id')
            timestamp = message.get('timestamp')
            
            # Check message type
            message_type = message.get('type')
            
            if message_type == 'text':
                # Handle text message
                text = message.get('text', {}).get('body', '')
                return self._process_text_message(from_number, text, message_id, timestamp)
                
            elif message_type == 'image':
                # Handle image message
                image_id = message.get('image', {}).get('id')
                caption = message.get('image', {}).get('caption', '')
                return self._process_image_message(from_number, image_id, caption, message_id, timestamp)
                
            elif message_type == 'document':
                # Handle document message
                document_id = message.get('document', {}).get('id')
                filename = message.get('document', {}).get('filename', '')
                caption = message.get('document', {}).get('caption', '')
                return self._process_document_message(from_number, document_id, filename, caption, message_id, timestamp)
                
            else:
                # Unsupported message type
                self.send_message(
                    from_number, 
                    "Sorry, I can only process text messages, images, and documents. Please try again with a supported format."
                )
                return {"status": "error", "message": f"Unsupported message type: {message_type}"}
                
        except Exception as e:
            logger.exception(f"Error processing incoming message: {str(e)}")
            return {"error": str(e)}
    
    def _process_text_message(self, from_number, text, message_id, timestamp):
        """
        Process an incoming text message
        
        Args:
            from_number (str): Sender's phone number
            text (str): Message text
            message_id (str): Message ID
            timestamp (int): Message timestamp
            
        Returns:
            dict: Processing result
        """
        # Check if this is a verification code
        from src.models.whatsapp import WhatsappVerification
        from src.main import db
        
        # Look for verification code in the message
        verification = WhatsappVerification.query.filter_by(
            phone_number=from_number,
            is_verified=False
        ).first()
        
        if verification and verification.verification_code in text:
            # Mark as verified
            verification.is_verified = True
            verification.verified_at = datetime.utcnow()
            db.session.commit()
            
            # Get user info
            from src.models.user import User
            user = User.query.get(verification.user_id)
            
            # Send welcome message
            self.send_welcome_message(from_number, user.username if user else "User")
            
            return {
                "status": "success", 
                "action": "verification", 
                "user_id": verification.user_id
            }
        
        # Check if user is verified and has credits
        from src.models.user import User
        from src.models.credit import Credit
        
        verification = WhatsappVerification.query.filter_by(
            phone_number=from_number,
            is_verified=True
        ).first()
        
        if not verification:
            self.send_message(
                from_number, 
                "Your WhatsApp number is not verified with our service. Please visit our website to register and connect your WhatsApp account."
            )
            return {"status": "error", "message": "Unverified phone number"}
        
        # Get user and check credits
        user = User.query.get(verification.user_id)
        credit = Credit.query.filter_by(user_id=verification.user_id).first()
        
        if not credit or credit.balance < 1:
            self.send_message(
                from_number, 
                "You don't have enough credits to use this service. Please visit our website to purchase more credits."
            )
            return {"status": "error", "message": "Insufficient credits"}
        
        # Process the text question
        # This would call the AI agent to generate an answer
        # For now, we'll just simulate a response
        
        # Check if PDF is requested
        pdf_requested = any(keyword in text.lower() for keyword in ["pdf", "pdf please", "send as pdf"])
        credits_needed = 3 if pdf_requested else 1  # 1 for text + 2 for PDF if requested
        
        if credit.balance < credits_needed:
            self.send_message(
                from_number, 
                f"You don't have enough credits for this request. You need {credits_needed} credits but have {credit.balance}. Please visit our website to purchase more credits."
            )
            return {"status": "error", "message": "Insufficient credits"}
        
        # Deduct credits
        credit.balance -= 1  # Deduct for text question
        
        # Log usage
        from src.models.usage import UsageLog
        usage_log = UsageLog(
            user_id=verification.user_id,
            service_type="text_question",
            credits_used=1,
            details=f"Question: {text}"
        )
        db.session.add(usage_log)
        
        # Generate answer (placeholder)
        answer = f"This is a simulated answer to your question: {text}"
        
        # Send response
        self.send_message(from_number, answer)
        
        # Handle PDF generation if requested
        if pdf_requested:
            # Deduct additional credits for PDF
            credit.balance -= 2
            
            # Log PDF usage
            pdf_usage = UsageLog(
                user_id=verification.user_id,
                service_type="pdf_generation",
                credits_used=2,
                details=f"PDF for question: {text}"
            )
            db.session.add(pdf_usage)
            
            # Send PDF message (placeholder)
            self.send_message(
                from_number, 
                "Your PDF is being generated and will be sent shortly."
            )
            
            # In a real implementation, we would generate and send the PDF here
        
        db.session.commit()
        
        return {
            "status": "success", 
            "action": "text_question", 
            "user_id": verification.user_id,
            "credits_used": credits_needed
        }
    
    def _process_image_message(self, from_number, image_id, caption, message_id, timestamp):
        """
        Process an incoming image message
        
        Args:
            from_number (str): Sender's phone number
            image_id (str): Image ID
            caption (str): Image caption
            message_id (str): Message ID
            timestamp (int): Message timestamp
            
        Returns:
            dict: Processing result
        """
        # Similar to text processing but for images
        # Would download the image, extract text, and process the question
        
        # Check if user is verified and has credits
        from src.models.whatsapp import WhatsappVerification
        from src.models.user import User
        from src.models.credit import Credit
        from src.main import db
        
        verification = WhatsappVerification.query.filter_by(
            phone_number=from_number,
            is_verified=True
        ).first()
        
        if not verification:
            self.send_message(
                from_number, 
                "Your WhatsApp number is not verified with our service. Please visit our website to register and connect your WhatsApp account."
            )
            return {"status": "error", "message": "Unverified phone number"}
        
        # Get user and check credits
        user = User.query.get(verification.user_id)
        credit = Credit.query.filter_by(user_id=verification.user_id).first()
        
        # Image questions cost 2 credits
        pdf_requested = caption and any(keyword in caption.lower() for keyword in ["pdf", "pdf please", "send as pdf"])
        credits_needed = 4 if pdf_requested else 2  # 2 for image + 2 for PDF if requested
        
        if not credit or credit.balance < credits_needed:
            self.send_message(
                from_number, 
                f"You don't have enough credits for this request. You need {credits_needed} credits but have {credit.balance if credit else 0}. Please visit our website to purchase mor
(Content truncated due to size limit. Use line ranges to read in chunks)