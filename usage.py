"""
Usage log model for the WhatsApp AI Agent Web Portal
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

from src.main import db

class UsageLog(db.Model):
    """Usage log model for tracking service usage"""
    
    __tablename__ = 'usage_logs'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False)
    service_type = db.Column(db.String(50), nullable=False)  # text_question, image_question, document_processing, pdf_generation
    credits_used = db.Column(db.Float, nullable=False)
    details = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, user_id, service_type, credits_used, details=None):
        self.id = str(uuid.uuid4())
        self.user_id = user_id
        self.service_type = service_type
        self.credits_used = credits_used
        self.details = details
    
    def __repr__(self):
        return f'<UsageLog {self.service_type} {self.credits_used}>'
