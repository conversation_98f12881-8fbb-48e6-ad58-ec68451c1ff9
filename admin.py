"""
Admin routes for the WhatsApp AI Agent Web Portal
"""

from flask import Blueprint, render_template, redirect, url_for, request, flash, session, jsonify
from src.models.user import User
from src.models.credit import Credit
from src.models.payment import Transaction
from src.models.whatsapp import WhatsappVerification
from src.models.usage import UsageLog
from src.main import db
from functools import wraps
from datetime import datetime, timedelta

admin_bp = Blueprint('admin', __name__)

# Admin authentication decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page', 'danger')
            return redirect(url_for('auth.login'))
        
        if 'is_admin' not in session or not session['is_admin']:
            flash('You do not have permission to access this page', 'danger')
            return redirect(url_for('dashboard.index'))
        
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/admin/dashboard')
@admin_required
def dashboard():
    """Render the admin dashboard page"""
    # Get system statistics
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    
    # Users registered in the last 30 days
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    new_users = User.query.filter(User.created_at >= thirty_days_ago).count()
    
    # WhatsApp connections
    total_whatsapp = WhatsappVerification.query.count()
    verified_whatsapp = WhatsappVerification.query.filter_by(is_verified=True).count()
    
    # Transactions
    total_transactions = Transaction.query.count()
    total_revenue = db.session.query(db.func.sum(Transaction.amount)).filter_by(transaction_type='purchase').scalar() or 0
    
    # Recent transactions
    recent_transactions = Transaction.query.order_by(Transaction.created_at.desc()).limit(10).all()
    
    # Usage statistics
    total_usage = UsageLog.query.count()
    usage_by_type = db.session.query(
        UsageLog.service_type,
        db.func.count(UsageLog.id).label('count')
    ).group_by(UsageLog.service_type).all()
    
    return render_template('admin/dashboard.html',
                          stats={
                              'total_users': total_users,
                              'active_users': active_users,
                              'new_users': new_users,
                              'total_whatsapp': total_whatsapp,
                              'verified_whatsapp': verified_whatsapp,
                              'total_transactions': total_transactions,
                              'total_revenue': total_revenue,
                              'total_usage': total_usage
                          },
                          usage_by_type=usage_by_type,
                          recent_transactions=recent_transactions)

@admin_bp.route('/admin/users')
@admin_required
def users():
    """Render the user management page"""
    # Get filter parameters
    is_active = request.args.get('is_active')
    has_whatsapp = request.args.get('has_whatsapp')
    search = request.args.get('search')
    
    # Base query
    query = User.query
    
    # Apply filters
    if is_active is not None:
        is_active_bool = is_active.lower() == 'true'
        query = query.filter_by(is_active=is_active_bool)
    
    if has_whatsapp is not None:
        has_whatsapp_bool = has_whatsapp.lower() == 'true'
        if has_whatsapp_bool:
            query = query.join(WhatsappVerification, User.id == WhatsappVerification.user_id)
        else:
            # Users without WhatsApp verification
            subquery = db.session.query(WhatsappVerification.user_id)
            query = query.filter(~User.id.in_(subquery))
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (User.username.like(search_term)) |
            (User.email.like(search_term)) |
            (User.first_name.like(search_term)) |
            (User.last_name.like(search_term))
        )
    
    # Get paginated results
    page = request.args.get('page', 1, type=int)
    per_page = 20
    users = query.order_by(User.created_at.desc()).paginate(page=page, per_page=per_page)
    
    return render_template('admin/users.html',
                          users=users,
                          current_filters={
                              'is_active': is_active,
                              'has_whatsapp': has_whatsapp,
                              'search': search
                          })

@admin_bp.route('/admin/users/<user_id>')
@admin_required
def user_details(user_id):
    """Render the user details page"""
    user = User.query.get(user_id)
    
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('admin.users'))
    
    # Get user's credit balance
    credit = Credit.query.filter_by(user_id=user_id).first()
    
    # Get user's WhatsApp verification
    whatsapp = WhatsappVerification.query.filter_by(user_id=user_id).first()
    
    # Get user's recent transactions
    transactions = Transaction.query.filter_by(user_id=user_id).order_by(Transaction.created_at.desc()).limit(10).all()
    
    # Get user's recent usage logs
    usage_logs = UsageLog.query.filter_by(user_id=user_id).order_by(UsageLog.created_at.desc()).limit(10).all()
    
    return render_template('admin/user_details.html',
                          user=user,
                          credit=credit,
                          whatsapp=whatsapp,
                          transactions=transactions,
                          usage_logs=usage_logs)

@admin_bp.route('/admin/users/<user_id>/toggle-status', methods=['POST'])
@admin_required
def toggle_user_status(user_id):
    """Handle toggling a user's active status"""
    user = User.query.get(user_id)
    
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('admin.users'))
    
    try:
        user.is_active = not user.is_active
        db.session.commit()
        
        status = 'activated' if user.is_active else 'deactivated'
        flash(f'User {user.username} has been {status}', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'An error occurred: {str(e)}', 'danger')
    
    return redirect(url_for('admin.user_details', user_id=user_id))

@admin_bp.route('/admin/users/<user_id>/adjust-credits', methods=['POST'])
@admin_required
def adjust_user_credits(user_id):
    """Handle adjusting a user's credit balance"""
    user = User.query.get(user_id)
    
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('admin.users'))
    
    amount = request.form.get('amount', type=float)
    reason = request.form.get('reason')
    
    if not amount:
        flash('Amount is required', 'danger')
        return redirect(url_for('admin.user_details', user_id=user_id))
    
    try:
        # Get or create credit record
        credit = Credit.query.filter_by(user_id=user_id).first()
        if not credit:
            credit = Credit(user_id=user_id, balance=0)
            db.session.add(credit)
        
        # Update credit balance
        credit.balance += amount
        credit.last_updated = datetime.utcnow()
        
        # Create transaction record
        transaction_type = 'adjustment_add' if amount > 0 else 'adjustment_remove'
        transaction = Transaction(
            user_id=user_id,
            amount=amount,
            transaction_type=transaction_type,
            description=reason or 'Admin adjustment',
            status='completed'
        )
        db.session.add(transaction)
        
        db.session.commit()
        
        flash(f'Credit balance adjusted by {amount}', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'An error occurred: {str(e)}', 'danger')
    
    return redirect(url_for('admin.user_details', user_id=user_id))

@admin_bp.route('/admin/transactions')
@admin_required
def transactions():
    """Render the transactions page"""
    # Get filter parameters
    transaction_type = request.args.get('transaction_type')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Base query
    query = Transaction.query
    
    # Apply filters
    if transaction_type:
        query = query.filter_by(transaction_type=transaction_type)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Transaction.created_at >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Transaction.created_at <= date_to_obj)
        except ValueError:
            pass
    
    # Get paginated results
    page = request.args.get('page', 1, type=int)
    per_page = 20
    transactions = query.order_by(Transaction.created_at.desc()).paginate(page=page, per_page=per_page)
    
    # Get transaction types for filter dropdown
    transaction_types = db.session.query(Transaction.transaction_type).distinct().all()
    transaction_types = [t[0] for t in transaction_types]
    
    return render_template('admin/transactions.html',
                          transactions=transactions,
                          transaction_types=transaction_types,
                          current_filters={
                              'transaction_type': transaction_type,
                              'date_from': date_from,
                              'date_to': date_to
                          })

@admin_bp.route('/admin/usage-logs')
@admin_required
def usage_logs():
    """Render the usage logs page"""
    # Get filter parameters
    service_type = request.args.get('service_type')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Base query
    query = UsageLog.query
    
    # Apply filters
    if service_type:
        query = query.filter_by(service_type=service_type)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(UsageLog.created_at >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(UsageLog.created_at <= date_to_obj)
        except ValueError:
            pass
    
    # Get paginated results
    page = request.args.get('page', 1, type=int)
    per_page = 20
    usage_logs = query.order_by(UsageLog.created_at.desc()).paginate(page=page, per_page=per_page)
    
    # Get service types for filter dropdown
    service_types = db.session.query(UsageLog.service_type).distinct().all()
    service_types = [t[0] for t in service_types]
    
    return render_template('admin/usage_logs.html',
                          usage_logs=usage_logs,
                          service_types=service_types,
                          current_filters={
                              'service_type': service_type,
                              'date_from': date_from,
                              'date_to': date_to
                          })
