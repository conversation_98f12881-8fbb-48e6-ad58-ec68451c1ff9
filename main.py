"""
Main entry point for the WhatsApp AI Agent Web Portal
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))  # DON'T CHANGE THIS !!!

from flask import Flask, render_template, redirect, url_for, request, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import uuid
import datetime
import json

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev_secret_key')

# Database configuration - uncomment to enable
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(os.path.dirname(__file__)), 'whatsapp_ai_portal.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# Import models after db initialization
from src.models.user import User
from src.models.payment import PaymentMethod, Transaction
from src.models.credit import Credit
from src.models.whatsapp import WhatsappVerification
from src.models.usage import UsageLog

# Import routes
from src.routes.auth import auth_bp
from src.routes.dashboard import dashboard_bp
from src.routes.payment import payment_bp
from src.routes.whatsapp import whatsapp_bp
from src.routes.admin import admin_bp
from src.routes.api import api_bp

# Register blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(dashboard_bp)
app.register_blueprint(payment_bp)
app.register_blueprint(whatsapp_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(api_bp)

@app.route('/')
def index():
    """Render the homepage"""
    return render_template('index.html')

@app.route('/about')
def about():
    """Render the about page"""
    return render_template('about.html')

@app.route('/pricing')
def pricing():
    """Render the pricing page"""
    return render_template('pricing.html')

@app.route('/faq')
def faq():
    """Render the FAQ page"""
    return render_template('faq.html')

@app.route('/contact')
def contact():
    """Render the contact page"""
    return render_template('contact.html')

@app.route('/terms')
def terms():
    """Render the terms and conditions page"""
    return render_template('terms.html')

@app.route('/privacy')
def privacy():
    """Render the privacy policy page"""
    return render_template('privacy.html')

@app.errorhandler(404)
def page_not_found(e):
    """Handle 404 errors"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def server_error(e):
    """Handle 500 errors"""
    return render_template('500.html'), 500

# Create database tables
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
