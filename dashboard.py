"""
Dashboard routes for the WhatsApp AI Agent Web Portal
"""

from flask import Blueprint, render_template, redirect, url_for, request, flash, session
from src.models.user import User
from src.models.credit import Credit
from src.models.whatsapp import WhatsappVerification
from src.models.usage import UsageLog
from src.main import db
from functools import wraps

dashboard_bp = Blueprint('dashboard', __name__)

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@dashboard_bp.route('/dashboard')
@login_required
def index():
    """Render the main dashboard page"""
    user_id = session.get('user_id')
    user = User.query.get(user_id)
    
    if not user:
        session.clear()
        flash('User not found', 'danger')
        return redirect(url_for('auth.login'))
    
    # Get credit balance
    credit = Credit.query.filter_by(user_id=user_id).first()
    credit_balance = credit.balance if credit else 0.0
    
    # Get WhatsApp verification status
    whatsapp = WhatsappVerification.query.filter_by(user_id=user_id).first()
    whatsapp_status = {
        'connected': whatsapp.is_verified if whatsapp else False,
        'phone_number': whatsapp.phone_number if whatsapp else None
    }
    
    # Get recent usage logs
    recent_usage = UsageLog.query.filter_by(user_id=user_id).order_by(UsageLog.created_at.desc()).limit(5).all()
    
    # Calculate usage statistics
    total_credits_used = db.session.query(db.func.sum(UsageLog.credits_used)).filter_by(user_id=user_id).scalar() or 0
    
    usage_by_type = db.session.query(
        UsageLog.service_type,
        db.func.sum(UsageLog.credits_used).label('total_credits'),
        db.func.count(UsageLog.id).label('count')
    ).filter_by(user_id=user_id).group_by(UsageLog.service_type).all()
    
    usage_stats = {
        'total_credits_used': total_credits_used,
        'by_type': {item[0]: {'credits': item[1], 'count': item[2]} for item in usage_by_type}
    }
    
    return render_template('dashboard/index.html', 
                          user=user, 
                          credit_balance=credit_balance,
                          whatsapp_status=whatsapp_status,
                          recent_usage=recent_usage,
                          usage_stats=usage_stats)

@dashboard_bp.route('/dashboard/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """Render and handle the user profile page"""
    user_id = session.get('user_id')
    user = User.query.get(user_id)
    
    if not user:
        session.clear()
        flash('User not found', 'danger')
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # Update basic info
        if first_name:
            user.first_name = first_name
        if last_name:
            user.last_name = last_name
        
        # Update email if changed
        if email and email != user.email:
            # Check if email is already in use
            existing_user = User.query.filter_by(email=email).first()
            if existing_user and existing_user.id != user_id:
                flash('Email is already in use', 'danger')
                return render_template('dashboard/profile.html', user=user)
            user.email = email
        
        # Update password if provided
        if current_password and new_password and confirm_password:
            from werkzeug.security import check_password_hash, generate_password_hash
            
            if not check_password_hash(user.password_hash, current_password):
                flash('Current password is incorrect', 'danger')
                return render_template('dashboard/profile.html', user=user)
            
            if new_password != confirm_password:
                flash('New passwords do not match', 'danger')
                return render_template('dashboard/profile.html', user=user)
            
            user.password_hash = generate_password_hash(new_password)
        
        try:
            db.session.commit()
            flash('Profile updated successfully', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'An error occurred: {str(e)}', 'danger')
        
        return redirect(url_for('dashboard.profile'))
    
    return render_template('dashboard/profile.html', user=user)

@dashboard_bp.route('/dashboard/usage-history')
@login_required
def usage_history():
    """Render the usage history page"""
    user_id = session.get('user_id')
    
    # Get filter parameters
    service_type = request.args.get('service_type')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Base query
    query = UsageLog.query.filter_by(user_id=user_id)
    
    # Apply filters
    if service_type:
        query = query.filter_by(service_type=service_type)
    
    if date_from:
        from datetime import datetime
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(UsageLog.created_at >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        from datetime import datetime
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(UsageLog.created_at <= date_to_obj)
        except ValueError:
            pass
    
    # Get paginated results
    page = request.args.get('page', 1, type=int)
    per_page = 20
    usage_logs = query.order_by(UsageLog.created_at.desc()).paginate(page=page, per_page=per_page)
    
    # Get service types for filter dropdown
    service_types = db.session.query(UsageLog.service_type).filter_by(user_id=user_id).distinct().all()
    service_types = [t[0] for t in service_types]
    
    return render_template('dashboard/usage_history.html', 
                          usage_logs=usage_logs,
                          service_types=service_types,
                          current_filters={
                              'service_type': service_type,
                              'date_from': date_from,
                              'date_to': date_to
                          })
