// Initialize Stripe.js
const stripe = Stripe('pk_test_sample');

document.addEventListener('DOMContentLoaded', function() {
    // Handle payment form submission
    const paymentForm = document.getElementById('payment-form');
    if (paymentForm) {
        paymentForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            
            const submitButton = document.getElementById('submit-payment');
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            
            try {
                // Create payment method
                const { paymentMethod, error } = await stripe.createPaymentMethod({
                    type: 'card',
                    card: {
                        number: document.getElementById('card-number').value,
                        exp_month: document.getElementById('card-expiry-month').value,
                        exp_year: document.getElementById('card-expiry-year').value,
                        cvc: document.getElementById('card-cvc').value
                    },
                    billing_details: {
                        name: document.getElementById('card-name').value
                    }
                });
                
                if (error) {
                    // Display error
                    const errorElement = document.getElementById('payment-errors');
                    errorElement.textContent = error.message;
                    errorElement.classList.remove('d-none');
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Complete Payment';
                    return;
                }
                
                // Submit payment method ID to server
                const response = await fetch('/payment/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        payment_method_id: paymentMethod.id,
                        package_id: document.querySelector('input[name="package_id"]:checked').value
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Payment successful, redirect to success page
                    window.location.href = result.redirect_url;
                } else {
                    // Display error
                    const errorElement = document.getElementById('payment-errors');
                    errorElement.textContent = result.message || 'An error occurred during payment processing.';
                    errorElement.classList.remove('d-none');
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Complete Payment';
                }
            } catch (err) {
                console.error('Payment error:', err);
                const errorElement = document.getElementById('payment-errors');
                errorElement.textContent = 'An unexpected error occurred. Please try again.';
                errorElement.classList.remove('d-none');
                submitButton.disabled = false;
                submitButton.innerHTML = 'Complete Payment';
            }
        });
    }
    
    // WhatsApp verification status checker
    const whatsappVerificationChecker = document.getElementById('whatsapp-verification-checker');
    if (whatsappVerificationChecker) {
        const checkStatus = async () => {
            try {
                const response = await fetch('/whatsapp/check-status');
                const data = await response.json();
                
                if (data.verified) {
                    // Update UI to show verification success
                    document.getElementById('verification-pending').classList.add('d-none');
                    document.getElementById('verification-success').classList.remove('d-none');
                    
                    // Redirect after a short delay
                    setTimeout(() => {
                        window.location.href = '/whatsapp/connection';
                    }, 3000);
                } else {
                    // Check again in 5 seconds
                    setTimeout(checkStatus, 5000);
                }
            } catch (error) {
                console.error('Error checking WhatsApp verification status:', error);
                // Retry after a longer delay on error
                setTimeout(checkStatus, 10000);
            }
        };
        
        // Start checking status
        checkStatus();
    }
});
