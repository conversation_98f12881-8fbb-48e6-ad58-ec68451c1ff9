/* Main stylesheet for WhatsApp AI Agent Web Portal */

/* Variables */
:root {
    --primary: #25D366;
    --primary-dark: #128C7E;
    --secondary: #34B7F1;
    --dark: #075E54;
    --light: #ECE5DD;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    line-height: 1.6;
}

.bg-primary {
    background-color: var(--primary) !important;
}

.bg-dark {
    background-color: var(--dark) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
    padding: 100px 0;
}

/* Feature Icons */
.feature-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 24px;
}

/* Step Circles */
.step-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    font-weight: bold;
    font-size: 18px;
    margin: 0 auto;
}

/* Testimonial Avatars */
.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--dark);
}

/* Dashboard Styles */
.sidebar {
    background-color: var(--dark);
    color: white;
    min-height: calc(100vh - 56px);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
}

.sidebar .nav-link:hover, .sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
}

.dashboard-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.stat-card {
    border-left: 4px solid var(--primary);
}

/* Credit Balance Display */
.credit-balance {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.credit-balance .balance-amount {
    font-size: 2.5rem;
    font-weight: bold;
}

/* WhatsApp Connection Status */
.connection-status {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: bold;
    display: inline-block;
}

.connection-status.connected {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--success);
}

.connection-status.disconnected {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--danger);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(37, 211, 102, 0.25);
}

.form-label {
    font-weight: 500;
}

/* Payment Method Cards */
.payment-method-card {
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.payment-method-card:hover {
    border-color: var(--primary);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
}

.payment-method-card.default {
    border-color: var(--primary);
    background-color: rgba(37, 211, 102, 0.05);
}

/* Credit Package Selection */
.credit-package {
    border: 2px solid #ddd;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.credit-package:hover {
    border-color: var(--primary);
}

.credit-package.selected {
    border-color: var(--primary);
    background-color: rgba(37, 211, 102, 0.05);
}

/* Usage History Table */
.usage-table th {
    background-color: var(--light);
    font-weight: 600;
}

.service-badge {
    padding: 0.35rem 0.65rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.service-badge.text-question {
    background-color: rgba(52, 183, 241, 0.2);
    color: var(--secondary);
}

.service-badge.image-question {
    background-color: rgba(37, 211, 102, 0.2);
    color: var(--primary);
}

.service-badge.document-processing {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--warning);
}

.service-badge.pdf-generation {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--danger);
}

/* WhatsApp Verification Instructions */
.verification-code {
    font-size: 2rem;
    letter-spacing: 0.5rem;
    font-weight: bold;
    color: var(--primary);
    background-color: rgba(37, 211, 102, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    text-align: center;
}

.verification-steps {
    counter-reset: step-counter;
    list-style-type: none;
    padding-left: 0;
}

.verification-steps li {
    position: relative;
    padding-left: 3rem;
    margin-bottom: 1.5rem;
}

.verification-steps li::before {
    content: counter(step-counter);
    counter-increment: step-counter;
    position: absolute;
    left: 0;
    top: 0;
    width: 2rem;
    height: 2rem;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Admin Dashboard */
.admin-stat-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: white;
    border-left: 4px solid var(--primary);
}

.admin-stat-card .stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark);
}

.admin-stat-card .stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 50px 0;
    }
    
    .credit-balance .balance-amount {
        font-size: 2rem;
    }
    
    .sidebar {
        min-height: auto;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}
