"""
Payment models for the WhatsApp AI Agent Web Portal
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

from src.main import db

class PaymentMethod(db.Model):
    """Payment method model for storing user payment information"""
    
    __tablename__ = 'payment_methods'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    payment_type = db.Column(db.String(50), nullable=False)  # credit_card, paypal, etc.
    card_last_four = db.Column(db.String(4), nullable=True)
    expiry_date = db.Column(db.String(7), nullable=True)  # MM/YYYY
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    transactions = db.relationship('Transaction', backref='payment_method', lazy=True)
    
    def __init__(self, user_id, payment_type, card_last_four=None, expiry_date=None, is_default=False):
        self.id = str(uuid.uuid4())
        self.user_id = user_id
        self.payment_type = payment_type
        self.card_last_four = card_last_four
        self.expiry_date = expiry_date
        self.is_default = is_default
    
    def __repr__(self):
        return f'<PaymentMethod {self.payment_type} {self.card_last_four}>'


class Transaction(db.Model):
    """Transaction model for tracking payment and credit usage transactions"""
    
    __tablename__ = 'transactions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)  # Positive for purchases, negative for usage
    transaction_type = db.Column(db.String(50), nullable=False)  # purchase, usage
    description = db.Column(db.String(255), nullable=True)
    payment_method_id = db.Column(db.String(36), db.ForeignKey('payment_methods.id'), nullable=True)
    status = db.Column(db.String(50), nullable=False)  # completed, pending, failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, user_id, amount, transaction_type, description=None, payment_method_id=None, status='completed'):
        self.id = str(uuid.uuid4())
        self.user_id = user_id
        self.amount = amount
        self.transaction_type = transaction_type
        self.description = description
        self.payment_method_id = payment_method_id
        self.status = status
    
    def __repr__(self):
        return f'<Transaction {self.transaction_type} {self.amount}>'
