"""
Authentication routes for the WhatsApp AI Agent Web Portal
"""

from flask import Blueprint, render_template, redirect, url_for, request, flash, session
from werkzeug.security import generate_password_hash, check_password_hash
from src.models.user import User
from src.models.credit import Credit
from src.main import db
import uuid

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        
        # Validate form data
        if not username or not email or not password or not confirm_password:
            flash('All fields are required', 'danger')
            return render_template('auth/register.html')
        
        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return render_template('auth/register.html')
        
        # Check if username or email already exists
        existing_user = User.query.filter((User.username == username) | (User.email == email)).first()
        if existing_user:
            flash('Username or email already exists', 'danger')
            return render_template('auth/register.html')
        
        # Create new user
        password_hash = generate_password_hash(password)
        new_user = User(username=username, email=email, password_hash=password_hash, 
                        first_name=first_name, last_name=last_name)
        
        try:
            db.session.add(new_user)
            db.session.commit()
            
            # Initialize credit balance for new user
            new_credit = Credit(user_id=new_user.id, balance=0.0)
            db.session.add(new_credit)
            db.session.commit()
            
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('auth.login'))
        except Exception as e:
            db.session.rollback()
            flash(f'An error occurred: {str(e)}', 'danger')
            return render_template('auth/register.html')
    
    return render_template('auth/register.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login"""
    if request.method == 'POST':
        username_or_email = request.form.get('username_or_email')
        password = request.form.get('password')
        
        # Validate form data
        if not username_or_email or not password:
            flash('All fields are required', 'danger')
            return render_template('auth/login.html')
        
        # Find user by username or email
        user = User.query.filter((User.username == username_or_email) | (User.email == username_or_email)).first()
        
        if not user or not check_password_hash(user.password_hash, password):
            flash('Invalid credentials', 'danger')
            return render_template('auth/login.html')
        
        if not user.is_active:
            flash('Your account is inactive. Please contact support.', 'danger')
            return render_template('auth/login.html')
        
        # Update last login timestamp
        user.last_login = db.func.now()
        db.session.commit()
        
        # Set session variables
        session['user_id'] = user.id
        session['username'] = user.username
        session['is_admin'] = user.is_admin
        
        flash('Login successful!', 'success')
        
        # Redirect to admin dashboard if admin, otherwise to user dashboard
        if user.is_admin:
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('dashboard.index'))
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
def logout():
    """Handle user logout"""
    # Clear session
    session.clear()
    flash('You have been logged out', 'info')
    return redirect(url_for('index'))

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """Handle password recovery"""
    if request.method == 'POST':
        email = request.form.get('email')
        
        if not email:
            flash('Email is required', 'danger')
            return render_template('auth/forgot_password.html')
        
        # Find user by email
        user = User.query.filter_by(email=email).first()
        
        if not user:
            # Don't reveal if email exists or not for security
            flash('If your email is registered, you will receive password reset instructions', 'info')
            return render_template('auth/forgot_password.html')
        
        # In a real implementation, send password reset email
        # For now, just show a message
        flash('Password reset instructions have been sent to your email', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/forgot_password.html')

@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Handle password reset"""
    # In a real implementation, validate token
    # For now, just show the form
    
    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        if not password or not confirm_password:
            flash('All fields are required', 'danger')
            return render_template('auth/reset_password.html', token=token)
        
        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return render_template('auth/reset_password.html', token=token)
        
        # In a real implementation, find user by token and update password
        # For now, just show a message
        flash('Your password has been reset successfully', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password.html', token=token)
