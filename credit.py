"""
Credit model for the WhatsApp AI Agent Web Portal
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

from src.main import db

class Credit(db.Model):
    """Credit model for tracking user credit balance"""
    
    __tablename__ = 'credits'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False)
    balance = db.Column(db.Float, nullable=False, default=0.0)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, user_id, balance=0.0):
        self.id = str(uuid.uuid4())
        self.user_id = user_id
        self.balance = balance
    
    def __repr__(self):
        return f'<Credit {self.user_id} {self.balance}>'
