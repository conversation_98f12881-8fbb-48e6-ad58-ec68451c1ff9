"""
WhatsApp verification model for the WhatsApp AI Agent Web Portal
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

from src.main import db

class WhatsappVerification(db.Model):
    """WhatsApp verification model for storing user WhatsApp connection information"""
    
    __tablename__ = 'whatsapp_verification'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON>Key('users.id'), nullable=False)
    phone_number = db.Column(db.String(20), unique=True, nullable=False)
    verification_code = db.Column(db.String(10), nullable=False)
    is_verified = db.Column(db.<PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    verified_at = db.Column(db.DateTime, nullable=True)
    
    def __init__(self, user_id, phone_number, verification_code):
        self.id = str(uuid.uuid4())
        self.user_id = user_id
        self.phone_number = phone_number
        self.verification_code = verification_code
    
    def __repr__(self):
        return f'<WhatsappVerification {self.phone_number} {self.is_verified}>'
