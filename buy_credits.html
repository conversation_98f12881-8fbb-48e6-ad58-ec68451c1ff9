<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp AI Agent - Buy Credits</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fab fa-whatsapp me-2"></i>WhatsApp AI Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link active" href="{{ url_for('payment.buy_credits') }}">Buy Credits</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('whatsapp.whatsapp_connection') }}">WhatsApp Connection</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.usage_history') }}">Usage History</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.payment_methods') }}">Payment Methods</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.profile') }}">Profile</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">Welcome, {{ session.get('username') }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.index') }}" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.buy_credits') }}" class="nav-link active">
                                <i class="fas fa-coins"></i> Buy Credits
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('whatsapp.whatsapp_connection') }}" class="nav-link">
                                <i class="fab fa-whatsapp"></i> WhatsApp Connection
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.usage_history') }}" class="nav-link">
                                <i class="fas fa-history"></i> Usage History
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.payment_methods') }}" class="nav-link">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.profile') }}" class="nav-link">
                                <i class="fas fa-user-circle"></i> Profile
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 py-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h3 mb-4">Buy Credits</h1>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">Select Credit Package</h2>
                            </div>
                            <div class="card-body p-4">
                                <form action="{{ url_for('payment.process_credit_purchase') }}" method="post" id="creditPurchaseForm">
                                    <div class="row mb-4">
                                        {% for package in credit_packages %}
                                            <div class="col-md-4 mb-3">
                                                <div class="credit-package" data-package-id="{{ package.id }}">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="package_id" id="package_{{ package.id }}" value="{{ package.id }}" {% if loop.first %}checked{% endif %}>
                                                        <label class="form-check-label" for="package_{{ package.id }}">
                                                            <h3 class="h5 mb-2">{{ package.name }}</h3>
                                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                                <span class="badge bg-primary rounded-pill">{{ package.credits }} credits</span>
                                                                <span class="fw-bold">${{ package.price }}</span>
                                                            </div>
                                                            <p class="small text-muted mb-0">{{ package.description }}</p>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>

                                    <h3 class="h5 mb-3">Payment Method</h3>
                                    
                                    {% if payment_methods %}
                                        <div class="mb-4">
                                            {% for method in payment_methods %}
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="radio" name="payment_method_id" id="payment_method_{{ method.id }}" value="{{ method.id }}" {% if method.is_default or loop.first %}checked{% endif %}>
                                                    <label class="form-check-label" for="payment_method_{{ method.id }}">
                                                        {% if method.payment_type == 'credit_card' %}
                                                            <i class="far fa-credit-card me-2"></i>
                                                            Credit Card ending in {{ method.card_last_four }}
                                                            {% if method.expiry_date %}
                                                                (Expires: {{ method.expiry_date }})
                                                            {% endif %}
                                                            {% if method.is_default %}
                                                                <span class="badge bg-primary ms-2">Default</span>
                                                            {% endif %}
                                                        {% elif method.payment_type == 'paypal' %}
                                                            <i class="fab fa-paypal me-2"></i>
                                                            PayPal Account
                                                            {% if method.is_default %}
                                                                <span class="badge bg-primary ms-2">Default</span>
                                                            {% endif %}
                                                        {% else %}
                                                            <i class="fas fa-money-bill-wave me-2"></i>
                                                            {{ method.payment_type|title }}
                                                            {% if method.is_default %}
                                                                <span class="badge bg-primary ms-2">Default</span>
                                                            {% endif %}
                                                        {% endif %}
                                                    </label>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="alert alert-warning mb-4">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            You don't have any payment methods set up.
                                            <a href="{{ url_for('payment.add_payment_method') }}" class="alert-link">Add a payment method</a> to continue.
                                        </div>
                                    {% endif %}

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary btn-lg" {% if not payment_methods %}disabled{% endif %}>
                                            <i class="fas fa-shopping-cart me-2"></i> Purchase Credits
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">Credit Usage</h2>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Text Questions
                                        <span class="badge bg-primary rounded-pill">1 credit</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Image Questions
                                        <span class="badge bg-primary rounded-pill">2 credits</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Document Processing
                                        <span class="badge bg-primary rounded-pill">5 credits</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Question Paper Processing
                                        <span class="badge bg-primary rounded-pill">10 credits</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        PDF Generation
                                        <span class="badge bg-primary rounded-pill">2 credits</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">Payment Methods</h2>
                            </div>
                            <div class="card-body">
                                <p>Manage your payment methods or add a new one.</p>
                                <div class="d-grid gap-2">
                                    <a href="{{ url_for('payment.payment_methods') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-credit-card me-2"></i> Manage Payment Methods
                                    </a>
                                    <a href="{{ url_for('payment.add_payment_method') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-plus-circle me-2"></i> Add Payment Method
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script>
        // Make the entire credit package div clickable
        document.querySelectorAll('.credit-package').forEach(package => {
            package.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // Remove selected class from all packages
                document.querySelectorAll('.credit-package').forEach(p => {
                    p.classList.remove('selected');
                });
                
                // Add selected class to clicked package
                this.classList.add('selected');
            });
            
            // Add selected class to the default selected package
            const radio = package.querySelector('input[type="radio"]');
         
(Content truncated due to size limit. Use line ranges to read in chunks)