"""
Stripe payment integration for the WhatsApp AI Agent Web Portal
"""

import os
import stripe
import logging
from datetime import datetime
from flask import current_app, request, jsonify, session, redirect, url_for
from src.main import db
from src.models.payment import Payment, PaymentMethod
from src.models.credit import Credit, CreditPackage
from src.models.user import User

logger = logging.getLogger(__name__)

class StripePaymentGateway:
    """
    Class to handle Stripe payment processing
    """
    
    def __init__(self, api_key=None):
        """Initialize the Stripe payment gateway"""
        self.api_key = api_key or os.environ.get('STRIPE_SECRET_KEY')
        self.public_key = os.environ.get('STRIPE_PUBLIC_KEY')
        
        if not self.api_key:
            logger.warning("Stripe API key not provided")
            
        stripe.api_key = self.api_key
    
    def create_payment_intent(self, amount, currency='usd', metadata=None):
        """
        Create a payment intent in Stripe
        
        Args:
            amount (int): Amount in cents
            currency (str): Currency code
            metadata (dict): Additional metadata
            
        Returns:
            dict: Payment intent details
        """
        try:
            intent = stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                metadata=metadata or {}
            )
            return {
                'success': True,
                'client_secret': intent.client_secret,
                'id': intent.id
            }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating payment intent: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.exception(f"Error creating payment intent: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def process_payment(self, payment_method_id, amount, currency='usd', metadata=None):
        """
        Process a payment using a payment method
        
        Args:
            payment_method_id (str): Stripe payment method ID
            amount (int): Amount in cents
            currency (str): Currency code
            metadata (dict): Additional metadata
            
        Returns:
            dict: Payment processing result
        """
        try:
            payment_intent = stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                payment_method=payment_method_id,
                confirm=True,
                metadata=metadata or {}
            )
            
            return {
                'success': True,
                'payment_intent_id': payment_intent.id,
                'status': payment_intent.status
            }
        except stripe.error.CardError as e:
            logger.error(f"Card error processing payment: {str(e)}")
            return {
                'success': False,
                'error': e.user_message
            }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error processing payment: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.exception(f"Error processing payment: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def create_customer(self, email, name=None, metadata=None):
        """
        Create a Stripe customer
        
        Args:
            email (str): Customer email
            name (str): Customer name
            metadata (dict): Additional metadata
            
        Returns:
            dict: Customer creation result
        """
        try:
            customer = stripe.Customer.create(
                email=email,
                name=name,
                metadata=metadata or {}
            )
            
            return {
                'success': True,
                'customer_id': customer.id
            }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating customer: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.exception(f"Error creating customer: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def save_payment_method(self, payment_method_id, customer_id):
        """
        Save a payment method to a customer
        
        Args:
            payment_method_id (str): Stripe payment method ID
            customer_id (str): Stripe customer ID
            
        Returns:
            dict: Result of saving payment method
        """
        try:
            payment_method = stripe.PaymentMethod.attach(
                payment_method_id,
                customer=customer_id
            )
            
            # Set as default payment method
            stripe.Customer.modify(
                customer_id,
                invoice_settings={
                    'default_payment_method': payment_method_id
                }
            )
            
            return {
                'success': True,
                'payment_method': payment_method
            }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error saving payment method: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.exception(f"Error saving payment method: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def get_payment_method_details(self, payment_method_id):
        """
        Get details of a payment method
        
        Args:
            payment_method_id (str): Stripe payment method ID
            
        Returns:
            dict: Payment method details
        """
        try:
            payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
            
            if payment_method.type == 'card':
                return {
                    'success': True,
                    'type': 'credit_card',
                    'last_four': payment_method.card.last4,
                    'brand': payment_method.card.brand,
                    'exp_month': payment_method.card.exp_month,
                    'exp_year': payment_method.card.exp_year
                }
            else:
                return {
                    'success': True,
                    'type': payment_method.type
                }
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error retrieving payment method: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.exception(f"Error retrieving payment method: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def handle_webhook(self, payload, signature):
        """
        Handle Stripe webhook events
        
        Args:
            payload (str): Webhook payload
            signature (str): Webhook signature
            
        Returns:
            dict: Webhook handling result
        """
        webhook_secret = os.environ.get('STRIPE_WEBHOOK_SECRET')
        
        if not webhook_secret:
            logger.warning("Stripe webhook secret not configured")
            return {
                'success': False,
                'error': 'Webhook secret not configured'
            }
        
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, webhook_secret
            )
            
            # Handle specific event types
            if event['type'] == 'payment_intent.succeeded':
                return self._handle_payment_success(event['data']['object'])
            elif event['type'] == 'payment_intent.payment_failed':
                return self._handle_payment_failure(event['data']['object'])
            
            return {
                'success': True,
                'message': f"Unhandled event type: {event['type']}"
            }
            
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Signature verification failed: {str(e)}")
            return {
                'success': False,
                'error': 'Signature verification failed'
            }
        except Exception as e:
            logger.exception(f"Error handling webhook: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def _handle_payment_success(self, payment_intent):
        """
        Handle successful payment
        
        Args:
            payment_intent (dict): Payment intent object
            
        Returns:
            dict: Handling result
        """
        try:
            # Extract metadata
            metadata = payment_intent.get('metadata', {})
            user_id = metadata.get('user_id')
            package_id = metadata.get('package_id')
            
            if not user_id or not package_id:
                logger.error(f"Missing metadata in payment intent: {payment_intent.id}")
                return {
                    'success': False,
                    'error': 'Missing metadata in payment intent'
                }
            
            # Update payment record
            payment = Payment.query.filter_by(
                payment_intent_id=payment_intent.id
            ).first()
            
            if payment:
                payment.status = 'completed'
                payment.completed_at = datetime.utcnow()
                db.session.commit()
            
            # Add credits to user
            credit_package = CreditPackage.query.get(package_id)
            if not credit_package:
                logger.error(f"Credit package not found: {package_id}")
                return {
                    'success': False,
                    'error': 'Credit package not found'
                }
            
            credit = Credit.query.filter_by(user_id=user_id).first()
            if credit:
                credit.balance += credit_package.credits
                credit.last_updated = datetime.utcnow()
            else:
                credit = Credit(
                    user_id=user_id,
                    balance=credit_package.credits,
                    last_updated=datetime.utcnow()
                )
                db.session.add(credit)
            
            db.session.commit()
            
            logger.info(f"Payment successful: {payment_intent.id}, Credits added: {credit_package.credits}")
            
            return {
                'success': True,
                'message': 'Payment processed successfully'
            }
            
        except Exception as e:
            logger.exception(f"Error handling payment success: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }
    
    def _handle_payment_failure(self, payment_intent):
        """
        Handle failed payment
        
        Args:
            payment_intent (dict): Payment intent object
            
        Returns:
            dict: Handling result
        """
        try:
            # Update payment record
            payment = Payment.query.filter_by(
                payment_intent_id=payment_intent.id
            ).first()
            
            if payment:
                payment.status = 'failed'
                payment.error_message = payment_intent.get('last_payment_error', {}).get('message', 'Payment failed')
                db.session.commit()
            
            logger.info(f"Payment failed: {payment_intent.id}")
            
            return {
                'success': True,
                'message': 'Payment failure recorded'
            }
            
        except Exception as e:
            logger.exception(f"Error handling payment failure: {str(e)}")
            return {
                'success': False,
                'error': 'An unexpected error occurred'
            }


def process_credit_purchase(user_id, package_id, payment_method_id=None):
    """
    Process a credit purchase
    
    Args:
        user_id (int): User ID
        package_id (int): Credit package ID
        payment_method_id (str): Payment method ID (optional)
        
    Returns:
        dict: Processing result
    """
    try:
        # Get user and package
        user = User.query.get(user_id)
        package = CreditPackage.query.get(package_id)
        
        if not user:
            return {
                'success': False,
                'message': 'User not found'
            }
        
        if not package:
            return {
                'success': False,
                'message': 'Credit package not found'
            }
        
        # Initialize Stripe
        stripe_gateway = StripePaymentGateway()
        
        # If no payment method provided, use default
        if not payment_method_id:
            payment_method = PaymentMethod.query.filter_by(
                user_id=user_id,
                is_default=True
            ).first()
            
            if not payment_method:
                return {
                    'success': False,
                    'message': 'No default payment method found'
                }
            
            payment_method_id = payment_method.payment_method_id
        
        # Create payment record
        payment = Payment(
            user_id=user_id,
            amount=package.price,
            description=f"Purchase of {package.credits} credits",
            status='pending',
            payment_method_id=payment_method_id
        )
        db.session.add(payment)
        db.session.commit()
        
        # Process payment
        result = stripe_gateway.process_payment(
            payment_method_id=payment_method_id,
            amount=int(package.price * 100),  # Convert to cents
            metadata={
                'user_id': str(user_id),
                'package_id': str(package_id),
                'payment_id': str(payment.id)
            }
        )
        
        if not result['success']:
            payment.status = 'failed'
            payment.error_message = result.get('error', 'Payment processing failed')
            db.session.commit()
            
            return {
                'success': False,
                'message': result.get('error', 'Payment processing failed')
            }
        
        # Update payment record
        payment.payment_intent_id = result['payment_intent_id']
        payment.status = 'completed' if result['status'] == 'succeeded' else 'pending'
        
        if payment.status == 'completed':
            payment.completed_at = datetime.utcnow()
            
            # Add credits to user
            credit = Credit.query.filter_by(user_id=user_id).first()
            if credit:
                credit.balance += package.credits
                credit.last_updated = datetime.utcnow()
            else:
                credit = Credit(
                    user_id=user_id,
                    balance=package.credits,
                    last_updated=datetime.utcnow()
                )
                db.session.add(credit)
        
        d
(Content truncated due to size limit. Use line ranges to read in chunks)