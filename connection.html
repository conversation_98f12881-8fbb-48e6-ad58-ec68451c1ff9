<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp AI Agent - WhatsApp Connection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fab fa-whatsapp me-2"></i>WhatsApp AI Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.buy_credits') }}">Buy Credits</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link active" href="{{ url_for('whatsapp.whatsapp_connection') }}">WhatsApp Connection</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.usage_history') }}">Usage History</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('payment.payment_methods') }}">Payment Methods</a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link" href="{{ url_for('dashboard.profile') }}">Profile</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">Welcome, {{ session.get('username') }}</span>
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.index') }}" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.buy_credits') }}" class="nav-link">
                                <i class="fas fa-coins"></i> Buy Credits
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('whatsapp.whatsapp_connection') }}" class="nav-link active">
                                <i class="fab fa-whatsapp"></i> WhatsApp Connection
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.usage_history') }}" class="nav-link">
                                <i class="fas fa-history"></i> Usage History
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('payment.payment_methods') }}" class="nav-link">
                                <i class="fas fa-credit-card"></i> Payment Methods
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard.profile') }}" class="nav-link">
                                <i class="fas fa-user-circle"></i> Profile
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10 py-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h3 mb-4">WhatsApp Connection</h1>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body p-4">
                                {% if not has_credits %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        You need to purchase credits before connecting your WhatsApp account.
                                        <a href="{{ url_for('payment.buy_credits') }}" class="alert-link">Buy credits now</a>.
                                    </div>
                                {% endif %}

                                {% if whatsapp and whatsapp.is_verified %}
                                    <div class="text-center py-4">
                                        <div class="mb-4">
                                            <i class="fab fa-whatsapp fa-5x text-success"></i>
                                        </div>
                                        <h2 class="h4 mb-3">WhatsApp Connected</h2>
                                        <p class="mb-1">Your WhatsApp number is connected and ready to use.</p>
                                        <p class="mb-4"><strong>Phone Number:</strong> {{ whatsapp.phone_number }}</p>
                                        
                                        <div class="d-grid gap-2 col-md-6 mx-auto">
                                            <form action="{{ url_for('whatsapp.disconnect_whatsapp') }}" method="post">
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="fas fa-unlink me-2"></i> Disconnect WhatsApp
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="text-center py-4">
                                        <div class="mb-4">
                                            <i class="fab fa-whatsapp fa-5x text-muted"></i>
                                        </div>
                                        <h2 class="h4 mb-3">Connect Your WhatsApp</h2>
                                        <p class="mb-4">Connect your WhatsApp number to start using the AI agent service.</p>
                                        
                                        {% if whatsapp and not whatsapp.is_verified %}
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Your WhatsApp number is pending verification. Please complete the verification process.
                                                <a href="{{ url_for('whatsapp.verification_instructions', phone_number=whatsapp.phone_number) }}" class="alert-link">View verification instructions</a>.
                                            </div>
                                        {% else %}
                                            <form action="{{ url_for('whatsapp.register_whatsapp') }}" method="post" class="col-md-8 mx-auto">
                                                <div class="mb-3">
                                                    <label for="phone_number" class="form-label">WhatsApp Phone Number</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                                        <input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="+1234567890" required {% if not has_credits %}disabled{% endif %}>
                                                    </div>
                                                    <div class="form-text">Enter your full phone number with country code (e.g., +1234567890)</div>
                                                </div>
                                                <div class="d-grid">
                                                    <button type="submit" class="btn btn-primary" {% if not has_credits %}disabled{% endif %}>
                                                        <i class="fas fa-link me-2"></i> Connect WhatsApp
                                                    </button>
                                                </div>
                                            </form>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">How It Works</h2>
                            </div>
                            <div class="card-body">
                                <ol class="verification-steps">
                                    <li>
                                        <h3 class="h6">Enter Your Phone Number</h3>
                                        <p>Provide your WhatsApp phone number with country code.</p>
                                    </li>
                                    <li>
                                        <h3 class="h6">Get Verification Code</h3>
                                        <p>We'll generate a unique verification code for your account.</p>
                                    </li>
                                    <li>
                                        <h3 class="h6">Send Code via WhatsApp</h3>
                                        <p>Send the verification code to our WhatsApp business number.</p>
                                    </li>
                                    <li>
                                        <h3 class="h6">Start Using the Service</h3>
                                        <p>Once verified, you can start sending questions to our AI agent.</p>
                                    </li>
                                </ol>
                            </div>
                        </div>

                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white">
                                <h2 class="h5 mb-0">Usage Tips</h2>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li class="mb-2">Send text questions directly in a message</li>
                                    <li class="mb-2">Send images containing text or questions</li>
                                    <li class="mb-2">Upload documents or question papers</li>
                                    <li class="mb-2">Type "PDF please" to get answers in PDF format</li>
                                    <li>Check your credit balance regularly</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script>
        // Check WhatsApp verification status periodically if pending verification
        {% if whatsapp and not whatsapp.is_verified %}
        (function checkVerificationStatus() {
            fetch('{{ url_for("whatsapp.check_whatsapp_status") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.is_verified) {
                        window.location.reload();
                    } else {
                        setTimeout(checkVerificationStatus, 5000); // Check every 5 seconds
                    }
                })
                .catch(error => {
                    console.error('Error checking verification status:', error);
                    setTimeout(checkVerificationStatus, 10000); // Retry after 10 seconds on error
                });
        })();
        {% endif %}
    </script>
</body>
</html>
